import type { MetaFunction } from "@remix-run/cloudflare";
import { <PERSON> } from "@remix-run/react";
import { <PERSON><PERSON> } from "@repo/ui-kit";
import { CTAS<PERSON><PERSON>, Footer, Header, HeroSection } from "~/components";

export const meta: MetaFunction = () => {
    return [
        { title: "Blog - AI Chat Platform" },
        {
            name: "description",
            content:
                "Stay updated with the latest insights, tutorials, and news about AI conversations and our platform",
        },
    ];
};

export default function Blog() {
    const blogPosts = [
        {
            id: 1,
            title: "The Future of AI Conversations: What to Expect in 2025",
            excerpt:
                "Explore the emerging trends and technologies that will shape how we interact with AI systems in the coming year.",
            author: "<PERSON>",
            date: "December 15, 2024",
            category: "AI Trends",
            readTime: "5 min read",
            image: "bg-gradient-to-br from-blue-400 to-blue-600",
        },
        {
            id: 2,
            title: "Building Trust in AI: Our Approach to Ethical AI Development",
            excerpt:
                "Learn about our commitment to developing AI systems that are transparent, fair, and beneficial for all users.",
            author: "<PERSON>",
            date: "November 28, 2024",
            category: "Ethics",
            readTime: "7 min read",
            image: "bg-gradient-to-br from-green-400 to-green-600",
        },
        {
            id: 3,
            title: "How to Get the Most Out of Your AI Assistant",
            excerpt:
                "Practical tips and strategies for maximizing productivity and getting better responses from AI chat systems.",
            author: "Michael Chen",
            date: "November 20, 2024",
            category: "Tutorial",
            readTime: "6 min read",
            image: "bg-gradient-to-br from-purple-400 to-purple-600",
        },
        {
            id: 4,
            title: "The Science Behind Natural Language Understanding",
            excerpt:
                "Dive deep into the technology that enables AI systems to understand and respond to human language naturally.",
            author: "Dr. Emily Rodriguez",
            date: "November 10, 2024",
            category: "Technology",
            readTime: "8 min read",
            image: "bg-gradient-to-br from-orange-400 to-orange-600",
        },
        {
            id: 5,
            title: "Customer Success Stories: Real-World AI Applications",
            excerpt:
                "Discover how businesses and individuals are using our AI chat platform to solve real problems.",
            author: "Lisa Wang",
            date: "October 30, 2024",
            category: "Case Studies",
            readTime: "4 min read",
            image: "bg-gradient-to-br from-pink-400 to-pink-600",
        },
        {
            id: 6,
            title: "Privacy and Security in AI Chat Systems",
            excerpt:
                "Understanding how we protect your data and ensure secure conversations with our AI platform.",
            author: "David Kim",
            date: "October 15, 2024",
            category: "Security",
            readTime: "6 min read",
            image: "bg-gradient-to-br from-indigo-400 to-indigo-600",
        },
    ];

    const categories = [
        "All",
        "AI Trends",
        "Technology",
        "Tutorial",
        "Ethics",
        "Case Studies",
        "Security",
    ];

    return (
        <div className="min-h-screen bg-white">
            <Header />

            {/* Hero Section */}
            <HeroSection
                title="AI Chat Blog"
                description="Insights, tutorials, and updates from the world of AI conversations. Stay informed about the latest developments in artificial intelligence and our platform."
                primaryCTA={{
                    text: "Latest Posts",
                    href: "#blog-posts",
                }}
                secondaryCTA={{
                    text: "Subscribe",
                    onClick: () => {
                        document
                            .getElementById("newsletter")
                            ?.scrollIntoView({ behavior: "smooth" });
                    },
                }}
                backgroundGradient="from-blue-50 to-purple-50"
            />

            {/* Category Filter */}
            <section className="py-8 bg-white border-b border-gray-200">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="flex flex-wrap gap-2 justify-center">
                        {categories.map((category) => (
                            <button
                                key={category}
                                className={`px-4 py-2 rounded-full text-sm font-medium transition-colors ${
                                    category === "All"
                                        ? "bg-blue-600 text-white"
                                        : "bg-gray-100 text-gray-600 hover:bg-gray-200"
                                }`}
                            >
                                {category}
                            </button>
                        ))}
                    </div>
                </div>
            </section>

            {/* Featured Post */}
            <section className="py-12">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="bg-white rounded-lg shadow-lg overflow-hidden">
                        <div className="md:flex">
                            <div className="md:w-1/2">
                                <div className="h-64 md:h-full bg-gradient-to-br from-blue-400 to-blue-600 flex items-center justify-center">
                                    <span className="text-4xl font-bold text-white">Featured</span>
                                </div>
                            </div>
                            <div className="md:w-1/2 p-8">
                                <div className="flex items-center mb-4">
                                    <span className="bg-blue-100 text-blue-800 text-xs font-semibold px-2.5 py-0.5 rounded">
                                        Featured
                                    </span>
                                    <span className="text-gray-500 text-sm ml-3">5 min read</span>
                                </div>
                                <h2 className="text-2xl md:text-3xl font-bold text-gray-900 mb-4">
                                    The Future of AI Conversations: What to Expect in 2025
                                </h2>
                                <p className="text-gray-600 mb-6">
                                    Explore the emerging trends and technologies that will shape how
                                    we interact with AI systems in the coming year. From advanced
                                    natural language processing to multimodal interactions, discover
                                    what's on the horizon.
                                </p>
                                <div className="flex items-center justify-between">
                                    <div className="flex items-center">
                                        <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center mr-3">
                                            <span className="text-white text-sm font-medium">
                                                SJ
                                            </span>
                                        </div>
                                        <div>
                                            <p className="text-sm font-medium text-gray-900">
                                                Sarah Johnson
                                            </p>
                                            <p className="text-xs text-gray-500">
                                                December 15, 2024
                                            </p>
                                        </div>
                                    </div>
                                    <Button>Read More</Button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            {/* Blog Posts Grid */}
            <section id="blog-posts" className="py-12 bg-gray-50">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-8">Latest Posts</h2>
                    <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                        {blogPosts.slice(1).map((post) => (
                            <article
                                key={post.id}
                                className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow"
                            >
                                <div
                                    className={`h-48 ${post.image} flex items-center justify-center`}
                                >
                                    <span className="text-2xl font-bold text-white">
                                        {post.category}
                                    </span>
                                </div>
                                <div className="p-6">
                                    <div className="flex items-center mb-3">
                                        <span className="bg-gray-100 text-gray-800 text-xs font-semibold px-2.5 py-0.5 rounded">
                                            {post.category}
                                        </span>
                                        <span className="text-gray-500 text-sm ml-3">
                                            {post.readTime}
                                        </span>
                                    </div>
                                    <h3 className="text-xl font-semibold text-gray-900 mb-3 line-clamp-2">
                                        {post.title}
                                    </h3>
                                    <p className="text-gray-600 mb-4 line-clamp-3">
                                        {post.excerpt}
                                    </p>
                                    <div className="flex items-center justify-between">
                                        <div className="flex items-center">
                                            <div className="w-6 h-6 bg-gray-400 rounded-full flex items-center justify-center mr-2">
                                                <span className="text-white text-xs font-medium">
                                                    {post.author
                                                        .split(" ")
                                                        .map((n) => n[0])
                                                        .join("")}
                                                </span>
                                            </div>
                                            <div>
                                                <p className="text-xs text-gray-900">
                                                    {post.author}
                                                </p>
                                                <p className="text-xs text-gray-500">{post.date}</p>
                                            </div>
                                        </div>
                                        <button className="text-blue-600 hover:text-blue-800 text-sm font-medium">
                                            Read More →
                                        </button>
                                    </div>
                                </div>
                            </article>
                        ))}
                    </div>
                </div>
            </section>

            {/* Newsletter Signup */}
            <div id="newsletter">
                <CTASection
                    title="Stay Updated"
                    description="Get the latest insights and updates delivered directly to your inbox."
                    primaryCTA={{
                        text: "Subscribe Now",
                        href: "/newsletter",
                    }}
                    secondaryCTA={{
                        text: "View Archive",
                        href: "/blog/archive",
                    }}
                />
            </div>

            <Footer />
        </div>
    );
}
