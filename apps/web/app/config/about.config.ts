/**
 * About page configuration
 */

import type { TeamMemberConfig } from './types';

// Team members
export const teamMembers: TeamMemberConfig[] = [
  {
    name: "<PERSON>",
    role: "CEO & Co-Founder",
    bio: "Former AI researcher at Google with 10+ years of experience in machine learning and natural language processing.",
    avatar: "/images/team/sarah-johnson.jpg",
    social: {
      twitter: "https://twitter.com/sarah<PERSON><PERSON><PERSON>",
      linkedin: "https://linkedin.com/in/sarah<PERSON><PERSON><PERSON>",
    }
  },
  {
    name: "<PERSON>",
    role: "CTO & Co-Founder",
    bio: "Ex-Cloudflare engineer specializing in edge computing and distributed systems architecture.",
    avatar: "/images/team/michael-chen.jpg",
    social: {
      github: "https://github.com/michaelchen",
      linkedin: "https://linkedin.com/in/michaelchen",
    }
  },
  {
    name: "<PERSON>",
    role: "Head of Product",
    bio: "Product leader with experience at Stripe and Notion, passionate about building user-centric AI experiences.",
    avatar: "/images/team/emily-rodriguez.jpg",
    social: {
      twitter: "https://twitter.com/emilyrodriguez",
      linkedin: "https://linkedin.com/in/emilyrodriguez",
    }
  },
  {
    name: "David <PERSON>",
    role: "Head of Engineering",
    bio: "Full-stack engineer and former tech lead at Uber, expert in scalable backend systems and AI infrastructure.",
    avatar: "/images/team/david-kim.jpg",
    social: {
      github: "https://github.com/davidkim",
      linkedin: "https://linkedin.com/in/davidkim",
    }
  },
];

// Company milestones
export const milestones = [
  {
    year: "2024",
    title: "Company Founded",
    description: "AI Chat Platform was founded with the vision of making AI conversations accessible to everyone."
  },
  {
    year: "2024",
    title: "Seed Funding",
    description: "Raised $2M in seed funding from leading AI and infrastructure investors."
  },
  {
    year: "2024",
    title: "Beta Launch",
    description: "Launched private beta with 100 early adopters and achieved 99.9% uptime."
  },
  {
    year: "2024",
    title: "Public Launch",
    description: "Officially launched to the public with enterprise-grade security and global edge network."
  },
];

// About page configuration
export const aboutConfig = {
  hero: {
    title: "Building the Future of AI Conversations",
    description: "We're on a mission to make AI conversations more natural, secure, and accessible for everyone around the world.",
    image: "/images/about-hero.jpg",
  },
  mission: {
    title: "Our Mission",
    description: "To democratize access to advanced AI conversation technology while maintaining the highest standards of security, privacy, and user experience.",
  },
  vision: {
    title: "Our Vision",
    description: "A world where AI-powered conversations enhance human communication and productivity, making complex tasks simple and accessible to all.",
  },
  values: [
    {
      title: "Innovation First",
      description: "We continuously push the boundaries of what's possible with AI technology.",
      icon: "lightbulb",
    },
    {
      title: "User-Centric",
      description: "Every decision we make is guided by what's best for our users and their experience.",
      icon: "users",
    },
    {
      title: "Security & Privacy",
      description: "We believe privacy is a fundamental right and security is non-negotiable.",
      icon: "shield",
    },
    {
      title: "Global Impact",
      description: "We're building technology that can positively impact people worldwide.",
      icon: "globe",
    },
  ],
  team: {
    title: "Meet Our Team",
    description: "We're a diverse team of engineers, researchers, and designers passionate about AI and user experience.",
    members: teamMembers,
  },
  story: {
    title: "Our Story",
    description: "Founded in 2024 by AI researchers and infrastructure engineers who saw the need for faster, more secure AI conversations.",
    milestones,
  },
  investors: {
    title: "Backed by Leading Investors",
    description: "We're proud to be supported by investors who share our vision for the future of AI.",
    logos: [
      { name: "Acme Ventures", logo: "/images/investors/acme-ventures.svg" },
      { name: "Tech Capital", logo: "/images/investors/tech-capital.svg" },
      { name: "Innovation Fund", logo: "/images/investors/innovation-fund.svg" },
    ]
  },
  careers: {
    title: "Join Our Team",
    description: "We're always looking for talented individuals who are passionate about AI and want to make a global impact.",
    cta: {
      text: "View Open Positions",
      href: "/careers",
    }
  }
};
