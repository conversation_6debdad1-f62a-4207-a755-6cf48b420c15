# 配置数据管理系统

这个配置系统实现了"配置即代码"的理念，同时支持动态配置更新，为应用提供了灵活且类型安全的配置管理方案。

## 🏗️ 目录结构

```
app/config/
├── index.ts              # 统一导出入口
├── types.ts              # 类型定义
├── manager.ts            # 配置管理器
├── ui/                   # UI组件配置
│   ├── navigation.ts     # 导航配置
│   ├── footer.ts         # Footer配置
│   └── sidebar.ts        # 侧边栏配置
├── landing/              # 落地页配置
│   ├── hero.ts           # Hero区域配置
│   ├── features.ts       # 功能特性配置
│   ├── pricing.ts        # 定价配置
│   ├── testimonials.ts   # 用户证言配置
│   ├── stats.ts          # 统计数据配置
│   └── faq.ts            # FAQ配置
├── blog/                 # 博客配置
├── about/                # 关于页面配置
├── legal/                # 法律文档配置
├── business/             # 业务配置
│   ├── company.ts        # 公司信息
│   ├── contact.ts        # 联系信息
│   └── social.ts         # 社交媒体
└── examples/             # 使用示例
    └── usage.tsx         # 组件使用示例
```

## 🚀 快速开始

### 1. 静态配置使用

```tsx
import { pricingPlans, featureShowcase } from "~/config/landing";
import { PricingGrid, FeatureGrid } from "~/components";

export function LandingPage() {
  return (
    <div>
      <FeatureGrid
        title={featureShowcase.sections.main.title}
        features={featureShowcase.sections.main.features}
        columns={featureShowcase.sections.main.columns}
      />
      
      <PricingGrid
        title="选择适合您的方案"
        plans={pricingPlans}
      />
    </div>
  );
}
```

### 2. 动态配置使用

```tsx
import { useEffect, useState } from "react";
import { getConfig } from "~/config/manager";
import { pricingPlans } from "~/config/landing";

export function DynamicPricingPage() {
  const [plans, setPlans] = useState(pricingPlans);

  useEffect(() => {
    async function loadConfig() {
      const dynamicPlans = await getConfig(
        'pricing-plans',
        pricingPlans,
        {
          apiEndpoint: '/api/config/pricing-plans',
          ttl: 600, // 10分钟缓存
        }
      );
      setPlans(dynamicPlans);
    }
    loadConfig();
  }, []);

  return <PricingGrid plans={plans} />;
}
```

### 3. 自定义Hook使用

```tsx
import { useConfig } from "~/config/examples/usage";
import { pricingPlans } from "~/config/landing";

export function SmartPricingPage() {
  const { data: plans, loading, error } = useConfig(
    'pricing-plans',
    pricingPlans,
    {
      apiEndpoint: '/api/config/pricing-plans',
      ttl: 300,
      autoRefresh: true,
    }
  );

  if (loading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return <PricingGrid plans={plans} />;
}
```

## 📝 配置类型

### 基础配置接口

```typescript
interface BaseConfig {
  id: string;
  version: string;
  lastUpdated: string;
  enabled: boolean;
}

interface DynamicConfig<T = any> extends BaseConfig {
  data: T;
  source: 'static' | 'api' | 'cms';
  cacheKey?: string;
  ttl?: number;
}
```

### 常用配置类型

- `FeatureConfig` - 功能特性配置
- `PricingPlan` - 定价方案配置
- `NavigationItem` - 导航项配置
- `TestimonialConfig` - 用户证言配置
- `BlogPostConfig` - 博客文章配置
- `FAQConfig` - FAQ配置

## 🔧 配置管理器

配置管理器提供了统一的配置获取和缓存机制：

```typescript
import { configManager } from "~/config/manager";

// 获取配置
const config = await configManager.getConfig('key', staticData, options);

// 设置缓存
configManager.setCache('key', data, ttl);

// 清除缓存
configManager.clearCache('key');

// 获取缓存信息
const cacheInfo = configManager.getCacheInfo();
```

## 🌐 动态配置API

### 获取配置

```
GET /api/config/:key
```

返回格式：
```json
{
  "id": "pricing-plans",
  "version": "1.0.0",
  "lastUpdated": "2024-01-01T00:00:00Z",
  "enabled": true,
  "data": [...],
  "source": "api",
  "ttl": 300
}
```

### 更新配置（需要管理员权限）

```
POST /api/config/:key
Content-Type: application/json

{
  "data": [...],
  "ttl": 300
}
```

## 🎯 最佳实践

### 1. 类型安全
- 为所有配置数据定义TypeScript接口
- 使用泛型确保配置管理器的类型安全

### 2. 缓存策略
- 为不同类型的配置设置合适的TTL
- 使用`forceRefresh`参数在必要时强制刷新

### 3. 错误处理
- 始终提供静态配置作为fallback
- 优雅处理网络错误和API失败

### 4. 性能优化
- 使用适当的缓存时间避免频繁请求
- 考虑使用React的`useMemo`缓存计算结果

### 5. 开发体验
- 在开发环境中使用较短的TTL便于测试
- 提供清晰的错误信息和日志

## 🔄 迁移指南

从硬编码数据迁移到配置系统：

1. **识别硬编码数据**：找出组件中的硬编码数据
2. **创建配置文件**：在相应目录下创建配置文件
3. **定义类型**：在`types.ts`中添加类型定义
4. **更新组件**：修改组件使用配置数据
5. **测试验证**：确保功能正常且类型安全

## 🚀 扩展功能

### 支持多语言

```typescript
interface I18nConfig<T> {
  [locale: string]: T;
}

const heroConfig: I18nConfig<HeroConfig> = {
  'zh-CN': { title: '下一代AI对话体验', ... },
  'en-US': { title: 'Next-Gen AI Chat Experience', ... },
};
```

### 环境特定配置

```typescript
const config = process.env.NODE_ENV === 'production' 
  ? productionConfig 
  : developmentConfig;
```

### A/B测试支持

```typescript
const config = await getConfig('hero', staticHero, {
  apiEndpoint: `/api/config/hero?variant=${userVariant}`,
});
```

这个配置系统为你的应用提供了强大而灵活的配置管理能力，支持从简单的静态配置到复杂的动态配置场景。
